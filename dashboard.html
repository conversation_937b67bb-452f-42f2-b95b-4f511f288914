<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bangladesh Unbound - Dashboard</title>
    <!-- Bootstrap 5.3.2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-green: #2d7a3e;
            --light-green: #4caf50;
            --dark-bg: #2c2c2c;
            --sidebar-bg: #2d7a3e;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            background-color: var(--sidebar-bg);
            min-height: 100vh;
            width: 250px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        
        .sidebar .logo {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border: none;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }
        
        .main-content {
            margin-left: 250px;
            padding: 0;
        }
        
        .top-header {
            background-color: var(--dark-bg);
            color: white;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            gap: 10px;
            background-color: var(--light-green);
            padding: 8px 15px;
            border-radius: 25px;
        }
        
        .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: #ddd;
        }
        
        .content-area {
            padding: 30px;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #555;
            padding: 15px;
        }
        
        .table td {
            border: none;
            padding: 15px;
            vertical-align: middle;
        }
        
        .guide-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .guide-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #ddd;
        }
        
        .btn-view-details {
            background-color: transparent;
            border: 2px solid var(--light-green);
            color: var(--light-green);
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: 500;
        }
        
        .btn-view-details:hover {
            background-color: var(--light-green);
            color: white;
        }
        
        .btn-pay-now {
            background-color: var(--light-green);
            border: none;
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: 500;
        }
        
        .payment-info {
            font-size: 0.9rem;
        }
        
        .payment-paid {
            color: var(--light-green);
            font-weight: 600;
        }
        
        .payment-due {
            color: #dc3545;
            font-weight: 600;
        }
        
        .status-valid {
            color: var(--light-green);
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgwIiBoZWlnaHQ9IjQwIiB2aWV3Qm94PSIwIDAgMTgwIDQwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8dGV4dCB4PSIxMCIgeT0iMjUiIGZpbGw9IndoaXRlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiIGZvbnQtd2VpZ2h0PSJib2xkIj5CQU5HTEFERVNIPC90ZXh0Pgo8dGV4dCB4PSIxMCIgeT0iMzgiIGZpbGw9IndoaXRlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk9OQk9VTkQ8L3RleHQ+Cjwvc3ZnPgo=" alt="Bangladesh Unbound" class="img-fluid">
        </div>
        <nav class="nav flex-column">
            <a class="nav-link active" href="#"><i class="bi bi-grid-3x3-gap"></i> Dashboard</a>
            <a class="nav-link" href="#"><i class="bi bi-calendar-check"></i> Bookings</a>
            <a class="nav-link" href="#"><i class="bi bi-credit-card"></i> Payment</a>
            <a class="nav-link" href="#"><i class="bi bi-person"></i> Profile</a>
            <a class="nav-link" href="#"><i class="bi bi-box-arrow-right"></i> Logout</a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Header -->
        <div class="top-header">
            <h2 class="mb-0">Dashboard</h2>
            <div class="user-profile">
                <span>Hello, Nasim</span>
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzUiIGhlaWdodD0iMzUiIHZpZXdCb3g9IjAgMCAzNSAzNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTcuNSIgY3k9IjE3LjUiIHI9IjE3LjUiIGZpbGw9IiNkZGQiLz4KPHN2ZyB4PSI3IiB5PSI3IiB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+CjxwYXRoIGQ9Ik0yMCAyMXYtMmE0IDQgMCAwIDAtNC00SDhhNCA0IDAgMCAwLTQgNHYyIiBzdHJva2U9IiM2NjYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxjaXJjbGUgY3g9IjEyIiBjeT0iNyIgcj0iNCIgc3Ryb2tlPSIjNjY2IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4KPC9zdmc+" alt="User Avatar" class="user-avatar">
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- On Going Bookings -->
            <div class="mb-4">
                <h3 class="section-title">On Going Bookings</h3>
                <div class="table-container">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Tour Name</th>
                                <th>Package Details</th>
                                <th>Tour Date</th>
                                <th>Tour Guide</th>
                                <th>Tour Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Old Dhaka Tour</td>
                                <td>2 Days, 1 Night</td>
                                <td>02-03 May</td>
                                <td>
                                    <div class="guide-info">
                                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNkZGQiLz4KPHN2ZyB4PSI4IiB5PSI4IiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+CjxwYXRoIGQ9Ik0yMCAyMXYtMmE0IDQgMCAwIDAtNC00SDhhNCA0IDAgMCAwLTQgNHYyIiBzdHJva2U9IiM2NjYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxjaXJjbGUgY3g9IjEyIiBjeT0iNyIgcj0iNCIgc3Ryb2tlPSIjNjY2IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4KPC9zdmc+" alt="Guide" class="guide-avatar">
                                        <div>
                                            <div class="fw-semibold">Fahim Bakhtiar</div>
                                            <small class="text-muted">+880 1721001234</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <button class="btn btn-view-details">
                                        View Details <i class="bi bi-arrow-right"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Upcoming Bookings -->
            <div class="mb-4">
                <h3 class="section-title">Upcoming Bookings</h3>
                <div class="table-container">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Tour Name</th>
                                <th>Package Details</th>
                                <th>Tour Date</th>
                                <th>Payment</th>
                                <th>Tour Guide</th>
                                <th>Tour Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Old Dhaka Tour</td>
                                <td>2 Days, 1 Night</td>
                                <td>02-03 May</td>
                                <td>
                                    <div class="payment-info">
                                        <div class="payment-paid">$500 Paid</div>
                                        <div class="payment-due">$2,000 Due</div>
                                        <button class="btn btn-pay-now mt-2">
                                            Pay Now <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    <div class="guide-info">
                                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNkZGQiLz4KPHN2ZyB4PSI4IiB5PSI4IiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+CjxwYXRoIGQ9Ik0yMCAyMXYtMmE0IDQgMCAwIDAtNC00SDhhNCA0IDAgMCAwLTQgNHYyIiBzdHJva2U9IiM2NjYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxjaXJjbGUgY3g9IjEyIiBjeT0iNyIgcj0iNCIgc3Ryb2tlPSIjNjY2IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4KPC9zdmc+" alt="Guide" class="guide-avatar">
                                        <div>
                                            <div class="fw-semibold">Fahim Bakhtiar</div>
                                            <small class="text-muted">+880 1721001234</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <button class="btn btn-view-details">
                                        View Details <i class="bi bi-arrow-right"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Custom Package/Services Request -->
            <div class="mb-4">
                <h3 class="section-title">Custom Package/Services Request</h3>
                <div class="table-container">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Request ID</th>
                                <th>Package Details</th>
                                <th>Location</th>
                                <th>Status</th>
                                <th>Request Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>#346GF3W</td>
                                <td>2 Days, 1 Night</td>
                                <td>Dhaka</td>
                                <td><span class="status-valid">Valid for 3 days</span></td>
                                <td>
                                    <button class="btn btn-view-details">
                                        View Details <i class="bi bi-arrow-right"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5.3.2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
