:root {
    --primary-green: #0C8040;
    --light-green: #098f45;
    --dark-bg: #2c2c2c;
    /* --sidebar-bg: #0C8040; */
}

body {
    background-color: #f8f9fa;
    font-family: 'Poppins', sans-serif;
}

.sidebar {
    background-color: var(--primary-green);
    min-height: 100vh;
    width: 250px;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
}

.sidebar .logo {
    padding: 25px 20px;
    /* border-bottom: 1px solid rgba(255,255,255,0.1); */
    background-color: #042613;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 15px 20px;
    border: none;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.5s linear;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(0,0,0,0.1);
    color: white;
}

.main-content {
    margin-left: 250px;
    padding: 0;
}

.top-header {
    /* background-color: var(--dark-bg); */
    /* color: white; */
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 30px;
    background-color: var(--primary-green);
    padding: 10px 25px;
    border-radius: 75px;
    color: white;
    line-height: 1.1em;
    font-size: 15px;
}
.user-profile strong {
    font-size: 17px;
    font-weight: 600;
}

.user-avatar {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background-color: #ddd;
}

.content-area {
    padding: 30px;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 70px;
    overflow: hidden;
}

.table>thead {
    border-bottom: 1px solid #ddd;
}
.table th {
    background-color: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #000;
    padding: 15px;
}

.table td {
    border: none;
    padding: 15px;
    vertical-align: middle;
}

.table > :not(caption) > * > * {
    background-color: transparent !important;
    box-shadow: none !important;
}

.table-hover tbody tr {
    transition: all 0.3s linear !important; /* Adjust duration and timing function as needed */
}

.table-hover tbody tr:hover {
    background-color: #f0f0f0 !important; /* Choose your desired hover background color */
}


.guide-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.guide-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #ddd;
}

.btn {
    font-size: 15px;
}
.btn-view-details {
    background-color: transparent;
    border: 2px solid var(--light-green);
    color: var(--light-green);
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.5s linear;
}

.btn-view-details:hover {
    background-color: var(--light-green);
    color: white;
}

.btn-view-details img {
    transition: filter 0.5s linear;
}
.btn-view-details:hover img {
    filter: brightness(0) invert(1);
}

.btn-pay-now {
    background-color: var(--light-green);
    border: none;
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.5s linear;
}
.btn-pay-now:hover {
    background-color: var(--primary-green);
    color: white;
}

.payment-info {
    font-size: 1rem;
}

.payment-paid {
    color: var(--light-green);
    font-weight: 600;
}

.payment-due {
    color: #D10000;
    font-weight: 600;
}

.status-valid {
    color: var(--light-green);
    font-weight: 500;
}
